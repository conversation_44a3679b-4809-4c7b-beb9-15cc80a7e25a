import { authPostFormData } from "@/lib/authFetch";
import { useState, useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase';
import type { RealtimePostgresChangesPayload, RealtimeChannel } from '@supabase/supabase-js';

/**
 * Client-side wrapper around the letter generation API.
 * This intentionally keeps business logic on the server – the hook merely
 * starts a generation job and polls for its status.
 */
export type LetterGenerationStatus =
  | {
      status: "idle";
    }
  | {
      status: "processing";
      startedAt?: number;
    }
  | {
      status: "done";
      plainText: string;
      designHtml?: string;
      startedAt: number;
      structuredData?: any;
    }
  | {
      status: "error";
      error: string;
      startedAt?: number;
    };

// Types for the letter generation parameters
export interface LetterGenerationParams {
  // Job method and data
  jobDescription?: string;
  jobImage?: File;
  // Resume data for unauthenticated users
  unauthenticatedResumeFile?: File;
  unauthenticatedResumeFileName?: string;
  // Template selection
  templateId?: string;
}

/**
 * Kick off letter generation. Returns the generation ID, which can be fed
 * into `useGenerationStatus`.
 */
export async function startGeneration(
  params: LetterGenerationParams
): Promise<string> {
  const formData = new FormData();

  // Add job description if provided
  if (params.jobDescription) {
    formData.append('jobDescription', params.jobDescription);
  }

  // Add job image if provided
  if (params.jobImage) {
    formData.append('jobImage', params.jobImage);
  }

  // Add unauthenticated resume file if provided
  if (params.unauthenticatedResumeFile && params.unauthenticatedResumeFileName) {
    formData.append('unauthenticatedResumeFile', params.unauthenticatedResumeFile);
    formData.append('unauthenticatedResumeFileName', params.unauthenticatedResumeFileName);
  }

  // Add template ID
  if (params.templateId) {
    formData.append('templateId', params.templateId);
  }

  const res = await authPostFormData("/api/generate-application-letter", formData);

  if (!res.ok) {
    const message = (await res.text()) || "Gagal memulai proses generate letter.";
    throw new Error(message);
  }

  const data = (await res.json()) as { id: string };
  return data.id;
}

/**
 * React hook that subscribes to letter generation status updates via Supabase realtime.
 */
export function useGenerationStatus(id: string | null): LetterGenerationStatus {
  const [status, setStatus] = useState<LetterGenerationStatus>({ status: "idle" });
  const subscriptionRef = useRef<RealtimeChannel | null>(null);

  const transformLetterData = (letter: any): LetterGenerationStatus => {
    if (!letter) return { status: "processing" };

    switch (letter.status) {
      case 'done':
        return {
          status: "done",
          plainText: letter.plain_text || '',
          designHtml: letter.design_html || undefined,
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : Date.now(),
          structuredData: letter.structured_data ? (typeof letter.structured_data === 'string' ? JSON.parse(letter.structured_data) : letter.structured_data) : undefined
        };
      case 'error':
        return {
          status: "error",
          error: letter.error_message || 'Generation failed',
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : undefined
        };
      case 'processing':
        return {
          status: "processing",
          startedAt: letter.created_at ? new Date(letter.created_at).getTime() : undefined
        };
      default:
        return { status: "processing" };
    }
  };

  const fetchInitialStatus = async (letterId: string) => {
    try {
      const supabase = createClient();
      
      const { data: letter, error } = await supabase
        .from('letters')
        .select('*')
        .eq('id', letterId)
        .single();

      if (error) {
        console.error('Error fetching letter status:', error);
        setStatus({ status: "error", error: error.message });
        return;
      }

      setStatus(transformLetterData(letter));
    } catch (err) {
      console.error('Error fetching initial status:', err);
      setStatus({ status: "error", error: 'Failed to fetch status' });
    }
  };

  useEffect(() => {
    // Cleanup previous subscription if it exists
    if (subscriptionRef.current) {
      const supabase = createClient();
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }

    if (!id) {
      setStatus({ status: "idle" });
      return;
    }

    // Set initial processing state
    setStatus({ status: "processing" });

    // Fetch initial status
    fetchInitialStatus(id);

    // Set up realtime subscription
    const supabase = createClient();

    const subscription = supabase
      .channel(`letter_${id}_changes`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'letters',
          filter: `id=eq.${id}`
        },
        (payload: RealtimePostgresChangesPayload<any>) => {
          console.log('Realtime letter status change:', payload);
          
          if (payload.eventType === 'UPDATE') {
            const updatedStatus = transformLetterData(payload.new);
            setStatus(updatedStatus);
          }
        }
      )
      .subscribe();

    // Store subscription in ref
    subscriptionRef.current = subscription;

    // Cleanup subscription on unmount or id change
    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
        subscriptionRef.current = null;
      }
    };
  }, [id]);

  return status;
}